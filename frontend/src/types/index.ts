export interface BiographyJob {
    id: string;
    user_name: string;
    email?: string;
    uploaded_pdf_path?: string;
    output_pdf_path?: string;
    status: JobStatus;
    progress_percentage: number;
    error_message?: string;
    outline_content?: string;
    biography_content?: string;
    evaluation_content?: string;
    final_biography_content?: string;
    chapters_content?: string;
    chapter_theses?: string;
    generation_iterations?: string;
    total_chapters?: number;
    completed_chapters?: number;
    created_at: string;
    updated_at?: string;
    completed_at?: string;
    processing_time_seconds?: number;
    has_output?: boolean;
}

export enum JobStatus {
    PENDING = "pending",
    QUEUED = "queued",
    PROCESSING = "processing",
    OUTLINE_COMPLETE = "outline_complete",
    ITERATIVE_WRITING = "iterative_writing",
    WRITING_COMPLETE = "writing_complete",
    EVALUATION_COMPLETE = "evaluation_complete",
    REWRITE_COMPLETE = "rewrite_complete",
    GENERATING_PDF = "generating_pdf",
    COMPLETED = "completed",
    FAILED = "failed"
}

export interface AgentPrompt {
    id: string;
    agent_name: string;
    prompt_content: string;
    is_active: boolean;
    created_at: string;
    updated_at?: string;
    version: number;
}

export interface CreateJobRequest {
    user_name: string;
    email?: string;
}

export interface CreatePromptRequest {
    agent_name: string;
    prompt_content: string;
}

export interface UpdatePromptRequest {
    prompt_content?: string;
    is_active?: boolean;
}

export interface ApiResponse<T> {
    data: T;
    message?: string;
}

export interface PaginatedResponse<T> {
    items: T[];
    total: number;
    page: number;
    per_page: number;
}

export interface OpenAIModelSettings {
    openai_model: string;
    available_models: string[];
}

export interface AppSetting {
    id: string;
    setting_key: string;
    setting_value: string;
    description?: string;
    is_active: boolean;
    created_at: string;
    updated_at?: string;
} 