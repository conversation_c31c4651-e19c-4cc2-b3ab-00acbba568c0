import axios from 'axios';
import { BiographyJob, AgentPrompt, CreatePromptRequest, UpdatePromptRequest, OpenAIModelSettings, AppSetting } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
    timeout: 300000, // 5 minutes for large uploads
});

// WebSocket connections management
const activeConnections = new Map<string, WebSocket>();

// Biography Job API
export const biographyApi = {
    // Upload PDF and create job
    uploadPDF: async (file: File, userName: string, email?: string): Promise<BiographyJob> => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('user_name', userName);
        if (email) {
            formData.append('email', email);
        }

        const response = await api.post('/api/biography/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    },

    // Start processing a job
    startProcessing: async (jobId: string): Promise<{ message: string; job_id: string; status: string }> => {
        const response = await api.post(`/api/biography/process/${jobId}`);
        return response.data;
    },

    // Get all jobs
    getJobs: async (): Promise<BiographyJob[]> => {
        const response = await api.get('/api/biography/jobs/summary');
        return response.data;
    },

    // Get single job
    getJob: async (jobId: string): Promise<BiographyJob> => {
        const response = await api.get(`/api/biography/jobs/${jobId}`);
        return response.data;
    },

    // Get job status (fallback for old API)
    getJobStatus: async (jobId: string): Promise<BiographyJob> => {
        const response = await api.get(`/api/biography/jobs/${jobId}/status`);
        return response.data;
    },

    // Subscribe to job status updates via WebSocket
    subscribeToJobStatus: (
        jobId: string,
        onUpdate: (job: BiographyJob) => void,
        onError?: (error: Event) => void,
        onComplete?: () => void
    ): WebSocket => {
        const shortJobId = jobId.slice(-8);
        console.log(`🚀 WebSocket: Starting subscription for job ${shortJobId}`);

        // Close existing connection if any
        if (activeConnections.has(jobId)) {
            const existingWs = activeConnections.get(jobId);
            if (existingWs) {
                console.log(`🔄 WebSocket: Closing existing connection for job ${shortJobId}`);
                existingWs.close();
                activeConnections.delete(jobId);
            }
        }

        const wsUrl = `${API_BASE_URL.replace('http', 'ws')}/api/biography/jobs/${jobId}/ws`;
        console.log(`🔌 WebSocket: Attempting to connect to ${wsUrl}`);
        console.log(`🌐 WebSocket: API_BASE_URL: ${API_BASE_URL}`);

        try {
            const ws = new WebSocket(wsUrl);
            console.log(`📝 WebSocket: Created WebSocket instance for job ${shortJobId}`);
            console.log(`🔍 WebSocket: Initial state: ${ws.readyState} (CONNECTING=${WebSocket.CONNECTING})`);

            activeConnections.set(jobId, ws);
            console.log(`💾 WebSocket: Stored connection in activeConnections for job ${shortJobId}`);
            console.log(`📈 WebSocket: Total active connections: ${activeConnections.size}`);

            ws.onopen = (event) => {
                console.log(`🟢 WebSocket: Connection opened for job ${shortJobId}`, event);
                console.log(`🔍 WebSocket: Connection state: ${ws.readyState} (OPEN=${WebSocket.OPEN})`);
                console.log(`📋 WebSocket: Connection protocol: ${ws.protocol}`);
                console.log(`📋 WebSocket: Connection extensions: ${ws.extensions}`);
            };

            ws.onmessage = (event) => {

                console.log('+++=====>', event.data);

                try {
                    console.log(`📩 WebSocket: Raw message received for job ${shortJobId}:`, event.data);
                    console.log(`📏 WebSocket: Message length: ${event.data.length} characters`);
                    console.log(`🕐 WebSocket: Message timestamp: ${new Date().toISOString()}`);

                    // Handle ping/pong messages BEFORE trying to parse JSON
                    if (event.data === 'pong') {
                        console.log(`🏓 WebSocket: Pong received for job ${shortJobId}`);
                        return;
                    }

                    if (event.data === 'ping') {
                        console.log(`🏓 WebSocket: Ping received for job ${shortJobId}, sending pong`);
                        ws.send('pong');
                        return;
                    }

                    // Try to parse as JSON (job data)
                    const jobData = JSON.parse(event.data);
                    console.log(`📝 WebSocket: Parsed job data for job ${shortJobId}:`, jobData);
                    console.log(`📊 WebSocket: Job status: ${jobData.status}, Progress: ${jobData.progress_percentage}%`);
                    console.log(`🆔 WebSocket: Job ID from message: ${jobData.id}, Expected: ${jobId}`);
                    console.log(`🔄 WebSocket: Job updated_at: ${jobData.updated_at}`);

                    // Validate data before calling callback
                    if (!jobData.id) {
                        console.error(`❌ WebSocket: Missing job ID in message for job ${shortJobId}`);
                        return;
                    }

                    if (jobData.id !== jobId) {
                        console.error(`❌ WebSocket: Job ID mismatch! Expected: ${jobId}, Got: ${jobData.id}`);
                        return;
                    }

                    // Call update callback
                    console.log(`⚡ WebSocket: About to call onUpdate callback for job ${shortJobId}`);
                    console.log(`📦 WebSocket: Callback data:`, { status: jobData.status, progress: jobData.progress_percentage });

                    onUpdate(jobData);
                    console.log(`✅ WebSocket: Called onUpdate callback for job ${shortJobId} with status: ${jobData.status}, progress: ${jobData.progress_percentage}%`);

                    // Auto-close when job is completed or failed
                    if (jobData.status === 'completed' || jobData.status === 'failed') {
                        console.log(`🔚 WebSocket: Job ${shortJobId} finished with status: ${jobData.status}, closing connection`);
                        ws.close();
                        activeConnections.delete(jobId);
                        if (onComplete) {
                            console.log(`🎉 WebSocket: Calling onComplete callback for job ${shortJobId}`);
                            onComplete();
                        }
                    }
                } catch (error) {
                    console.error(`❌ WebSocket: Error parsing data for job ${shortJobId}:`, error);
                    console.error(`❌ WebSocket: Raw data was:`, event.data);
                    console.error(`❌ WebSocket: Error stack:`, error);
                    ws.close();
                    activeConnections.delete(jobId);
                }
            };

            ws.onerror = (event) => {
                console.error(`💥 WebSocket: Connection error for job ${shortJobId}:`, event);
                console.error(`💥 WebSocket: Connection state: ${ws.readyState}`);
                console.error(`💥 WebSocket: Error type: ${event.type}`);
                console.error(`💥 WebSocket: Connection URL: ${wsUrl}`);
                activeConnections.delete(jobId);
                if (onError) {
                    console.log(`🚨 WebSocket: Calling onError callback for job ${shortJobId}`);
                    onError(event);
                }
            };

            ws.onclose = (event) => {
                console.log(`🔒 WebSocket: Connection closed for job ${shortJobId}`);
                console.log(`🔒 WebSocket: Close code: ${event.code}, Reason: "${event.reason}"`);
                console.log(`🔒 WebSocket: Was clean: ${event.wasClean}`);
                console.log(`🕐 WebSocket: Close timestamp: ${new Date().toISOString()}`);

                // Log close code meanings
                const closeCodeMeaning: { [key: number]: string } = {
                    1000: 'Normal closure',
                    1001: 'Going away',
                    1002: 'Protocol error',
                    1003: 'Unsupported data',
                    1005: 'No status received',
                    1006: 'Abnormal closure',
                    1007: 'Invalid frame payload data',
                    1008: 'Policy violation',
                    1009: 'Message too big',
                    1010: 'Missing extension',
                    1011: 'Internal error',
                    1015: 'TLS handshake error'
                };
                console.log(`🔒 WebSocket: Close code meaning: ${closeCodeMeaning[event.code] || 'Unknown'}`);

                activeConnections.delete(jobId);
                console.log(`📉 WebSocket: Remaining active connections: ${activeConnections.size}`);
            };

            // Test connection after a brief delay
            setTimeout(() => {
                console.log(`🔍 WebSocket: Connection state check for job ${shortJobId}: ${ws.readyState}`);
                if (ws.readyState === WebSocket.CONNECTING) {
                    console.log(`⏳ WebSocket: Still connecting for job ${shortJobId}...`);
                } else if (ws.readyState === WebSocket.OPEN) {
                    console.log(`✅ WebSocket: Connection established for job ${shortJobId}`);
                    // Send a test ping
                    try {
                        ws.send('ping');
                        console.log(`🏓 WebSocket: Sent ping to server for job ${shortJobId}`);
                    } catch (e: unknown) {
                        console.error(`❌ WebSocket: Failed to send ping for job ${shortJobId}:`, e);
                    }
                } else {
                    console.error(`❌ WebSocket: Connection failed for job ${shortJobId}, state: ${ws.readyState}`);
                }
            }, 1000);

            return ws;
        } catch (error: unknown) {
            console.error(`💥 WebSocket: Failed to create WebSocket for job ${shortJobId}:`, error);
            console.error(`💥 WebSocket: Error details:`, {
                message: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : 'No stack trace',
                url: wsUrl
            });
            throw error;
        }
    },

    // Close WebSocket connection for a job
    closeJobConnection: (jobId: string) => {
        const ws = activeConnections.get(jobId);
        if (ws) {
            ws.close();
            activeConnections.delete(jobId);
            console.log(`🔒 WebSocket: Manually closed connection for job ${jobId}`);
        }
    },

    // Close all WebSocket connections
    closeAllConnections: () => {
        activeConnections.forEach((ws, jobId) => {
            ws.close();
            console.log(`🔒 WebSocket: Closed connection for job ${jobId}`);
        });
        activeConnections.clear();
        console.log(`🔒 WebSocket: All connections closed`);
    },

    // Download biography PDF
    downloadBiography: async (jobId: string): Promise<Blob> => {
        const response = await api.get(`/api/biography/jobs/${jobId}/download`, {
            responseType: 'blob',
        });
        return response.data;
    },

    // Delete job
    deleteJob: async (jobId: string): Promise<{ message: string }> => {
        const response = await api.delete(`/api/biography/jobs/${jobId}`);
        return response.data;
    },

    // Reset job status
    resetJob: async (jobId: string): Promise<{ message: string; job_id: string }> => {
        const response = await api.post(`/api/biography/jobs/${jobId}/reset`);
        return response.data;
    },

    // Fallback: Poll job status (if WebSocket doesn't work)
    pollJobStatus: (
        jobId: string,
        onUpdate: (job: BiographyJob) => void,
        onComplete?: () => void,
        intervalMs: number = 2000
    ): (() => void) => {
        console.log(`🔄 POLLING: Starting polling for job ${jobId.slice(-8)} every ${intervalMs}ms`);

        const poll = async () => {
            try {
                const job = await biographyApi.getJob(jobId);
                console.log(`📊 POLLING: Got job status:`, job.status, `progress:`, job.progress_percentage);
                onUpdate(job);

                if (job.status === 'completed' || job.status === 'failed') {
                    console.log(`🏁 POLLING: Job ${jobId.slice(-8)} finished, stopping polling`);
                    clearInterval(intervalId);
                    if (onComplete) onComplete();
                }
            } catch (error: any) {
                console.error(`❌ POLLING: Error getting job status:`, error);

                // If job not found (404), stop polling
                if (error?.response?.status === 404) {
                    console.log(`🚫 POLLING: Job ${jobId.slice(-8)} not found (404), stopping polling`);
                    clearInterval(intervalId);
                    return;
                }

                // For other errors, continue polling but log them
                console.log(`⚠️ POLLING: Continuing polling despite error for job ${jobId.slice(-8)}`);
            }
        };

        const intervalId = setInterval(poll, intervalMs);

        // Return cleanup function
        return () => {
            console.log(`🛑 POLLING: Stopping polling for job ${jobId.slice(-8)}`);
            clearInterval(intervalId);
        };
    },
};

// Prompts API
export const promptsApi = {
    // Get all prompts
    getAllPrompts: async (): Promise<AgentPrompt[]> => {
        const response = await api.get('/api/prompts/');
        return response.data;
    },

    // Get prompt by agent name
    getPromptByAgent: async (agentName: string): Promise<AgentPrompt> => {
        const response = await api.get(`/api/prompts/${agentName}`);
        return response.data;
    },

    // Create new prompt
    createPrompt: async (promptData: CreatePromptRequest): Promise<AgentPrompt> => {
        const response = await api.post('/api/prompts/', promptData);
        return response.data;
    },

    // Update prompt
    updatePrompt: async (promptId: string, promptUpdate: UpdatePromptRequest): Promise<AgentPrompt> => {
        const response = await api.put(`/api/prompts/${promptId}`, promptUpdate);
        return response.data;
    },

    // Delete prompt
    deletePrompt: async (promptId: string): Promise<{ message: string }> => {
        const response = await api.delete(`/api/prompts/${promptId}`);
        return response.data;
    },

    // Initialize default prompts
    initializeDefaults: async (): Promise<{ message: string }> => {
        const response = await api.post('/api/prompts/initialize-defaults');
        return response.data;
    },

    // Debug prompts - get detailed information
    debugPrompts: async (): Promise<any> => {
        const response = await api.get('/api/prompts/debug');
        return response.data;
    },

    // Clear prompt cache
    clearCache: async (): Promise<{ message: string; cache_size_before: number; cache_size_after: number }> => {
        const response = await api.post('/api/prompts/clear-cache');
        return response.data;
    },

    // Test outline parsing
    testOutlineParsing: async (testData: { model?: string; sample_text?: string; target_chapters?: number }): Promise<any> => {
        const response = await api.post('/api/prompts/test-outline-parsing', testData);
        return response.data;
    },
};

// Settings API
export const settingsApi = {
    // Get OpenAI model settings
    getOpenAIModelSettings: async (): Promise<OpenAIModelSettings> => {
        const response = await api.get('/api/settings/openai-models');
        return response.data;
    },

    // Update OpenAI model
    updateOpenAIModel: async (modelName: string): Promise<{ message: string; model: string }> => {
        const response = await api.post('/api/settings/openai-model', null, {
            params: { model_name: modelName }
        });
        return response.data;
    },

    // Get all settings
    getAllSettings: async (): Promise<AppSetting[]> => {
        const response = await api.get('/api/settings/');
        return response.data;
    },
};

// Health check
export const healthApi = {
    check: async (): Promise<{ status: string; version: string }> => {
        const response = await api.get('/health');
        return response.data;
    },
};

export default api; 