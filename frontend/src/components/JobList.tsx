import React, { useState } from 'react';
import {
    Box,
    Paper,
    Typography,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    CircularProgress,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from '@mui/material';
import { BiographyJob, JobStatus } from '../types';
import { biographyApi } from '../services/api';
import { useTheme } from '@mui/material/styles';
import JobRow from './JobRow';

interface JobListProps {
    jobs: BiographyJob[];
    onJobUpdate: () => void;
    onJobStart: (jobId: string) => void;
    onJobReset?: (jobId: string) => void;
}

const JobList: React.FC<JobListProps> = ({ jobs, onJobUpdate, onJobStart, onJobReset }) => {
    const [selectedJob, setSelectedJob] = useState<BiographyJob | null>(null);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [jobToDelete, setJobToDelete] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [jobsPerPage, setJobsPerPage] = useState(10);
    const theme = useTheme();

    // Debug: track jobs prop changes
    React.useEffect(() => {
        console.log(`📊 JobList: Received ${jobs.length} jobs`, jobs.map(j => ({
            id: j.id.slice(-6),
            status: j.status,
            progress: j.progress_percentage
        })));
    }, [jobs]);

    // Calculate pagination
    const totalPages = Math.ceil(jobs.length / jobsPerPage);
    const startIndex = (currentPage - 1) * jobsPerPage;
    const endIndex = startIndex + jobsPerPage;
    const currentJobs = jobs.slice(startIndex, endIndex);

    // Reset to first page when jobs array changes significantly
    React.useEffect(() => {
        if (currentPage > totalPages && totalPages > 0) {
            setCurrentPage(1);
        }
    }, [jobs.length, totalPages, currentPage]);

    // REMOVE MEMOIZATION - process jobs directly to fix update issues
    const validatedJobs = React.useMemo(() => {
        console.log('🔍 JobList: Processing jobs without memoization...');
        console.log('📦 JobList: Raw jobs data:', currentJobs);

        if (!Array.isArray(currentJobs)) {
            console.error('❌ JobList: Current jobs is not an array:', currentJobs);
            return [];
        }

        const processed = currentJobs
            .filter((job, index) => {
                if (!job) {
                    console.error(`❌ JobList: Job at index ${index} is null/undefined`);
                    return false;
                }
                if (!job.id || typeof job.id !== 'string' || job.id.trim() === '') {
                    console.error(`❌ JobList: Job at index ${index} has invalid ID:`, job.id);
                    return false;
                }
                return true;
            })
            .map((job) => {
                // Create a new object each time to ensure React sees it as changed
                return {
                    ...job,
                    // Keep original ID from API
                    id: job.id,
                    // Ensure required fields have defaults
                    user_name: job.user_name || 'Unknown',
                    status: job.status || 'pending',
                    progress_percentage: job.progress_percentage || 0,
                    created_at: job.created_at || new Date().toISOString(),
                    // Add a render key to force updates
                    _renderKey: `${job.id}-${job.status}-${job.progress_percentage}-${Date.now()}`
                };
            });

        console.log('✅ JobList: Final processed jobs for rendering:', processed.map(j => ({
            id: j.id.slice(-6),
            status: j.status,
            progress: j.progress_percentage,
            renderKey: j._renderKey
        })));
        return processed;
    }, [currentJobs]); // Simplified dependency array

    // Debug: track validatedJobs changes
    React.useEffect(() => {
        console.log(`🎯 JobList: validatedJobs changed, rendering ${validatedJobs.length} jobs:`,
            validatedJobs.map(j => ({
                id: j.id.slice(-6),
                status: j.status,
                progress: j.progress_percentage
            }))
        );
    }, [validatedJobs]);

    // Add render logging
    console.log(`🎨 JobList: Component rendering with ${validatedJobs.length} jobs at ${new Date().toLocaleTimeString()}`);
    validatedJobs.forEach((job, index) => {
        console.log(`📋 JobList: Job ${index + 1}/${validatedJobs.length}: ${job.id.slice(-6)} - ${job.status} (${job.progress_percentage}%)`);
    });

    // Pagination handlers
    const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
        setCurrentPage(page);
    };

    const handleJobsPerPageChange = (event: any) => {
        setJobsPerPage(event.target.value);
        setCurrentPage(1); // Reset to first page when changing page size
    };

    const getStatusText = (status: JobStatus): string => {
        switch (status) {
            case JobStatus.PENDING:
                return 'Pending';
            case JobStatus.PROCESSING:
                return 'Processing';
            case JobStatus.OUTLINE_COMPLETE:
                return 'Outline Complete';
            case JobStatus.WRITING_COMPLETE:
                return 'Writing Complete';
            case JobStatus.EVALUATION_COMPLETE:
                return 'Evaluation Complete';
            case JobStatus.REWRITE_COMPLETE:
                return 'Rewrite Complete';
            case JobStatus.GENERATING_PDF:
                return 'Generating PDF';
            case JobStatus.COMPLETED:
                return 'Completed';
            case JobStatus.FAILED:
                return 'Failed';
            default:
                return status;
        }
    };

    const handleDownload = async (jobId: string) => {
        setLoading(true);
        setError(null);

        try {
            const blob = await biographyApi.downloadBiography(jobId);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `biography_${jobId}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (err: any) {
            let errorMessage = 'Download failed';

            if (err.response?.data?.detail) {
                // Handle single error message
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    // Handle validation errors array
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                } else {
                    // Handle object errors
                    errorMessage = JSON.stringify(err.response.data.detail);
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async () => {
        if (!jobToDelete) return;

        setLoading(true);
        setError(null);

        try {
            await biographyApi.deleteJob(jobToDelete);
            onJobUpdate();
            setDeleteDialogOpen(false);
            setJobToDelete(null);
        } catch (err: any) {
            let errorMessage = 'Delete failed';

            if (err.response?.data?.detail) {
                // Handle single error message
                if (typeof err.response.data.detail === 'string') {
                    errorMessage = err.response.data.detail;
                } else if (Array.isArray(err.response.data.detail)) {
                    // Handle validation errors array
                    errorMessage = err.response.data.detail.map((e: any) => e.msg || e.message || JSON.stringify(e)).join(', ');
                } else {
                    // Handle object errors
                    errorMessage = JSON.stringify(err.response.data.detail);
                }
            } else if (err.message) {
                errorMessage = err.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleView = (job: BiographyJob) => {
        setSelectedJob(job);
        setDialogOpen(true);
    };

    const handleReset = async (jobId: string) => {
        if (onJobReset) {
            onJobReset(jobId);
        }
    };

    return (
        <Paper elevation={3} sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
                Biography Jobs
            </Typography>

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                    <CircularProgress />
                    <Typography sx={{ ml: 2 }}>Loading jobs...</Typography>
                </Box>
            ) : validatedJobs.length === 0 ? (
                <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
                    No jobs found
                </Typography>
            ) : (
                <Box sx={{ overflowX: 'auto' }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                        <thead>
                            <tr style={{ backgroundColor: theme.palette.grey[50] }}>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>User</th>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Status</th>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Progress</th>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Created</th>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Processing Time</th>
                                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {validatedJobs.map((job, index) => (
                                <tr
                                    key={job.id}
                                    style={{
                                        backgroundColor: index % 2 === 0 ? 'white' : '#f5f5f5'
                                    }}
                                >
                                    <JobRow
                                        job={job}
                                        onView={handleView}
                                        onStart={onJobStart}
                                        onReset={handleReset}
                                        onDelete={(jobId) => {
                                            setJobToDelete(jobId);
                                            setDeleteDialogOpen(true);
                                        }}
                                        onDownload={handleDownload}
                                        onJobUpdate={onJobUpdate}
                                    />
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </Box>
            )}

            {/* Pagination Controls */}
            {jobs.length > 0 && (
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mt: 3,
                    flexWrap: 'wrap',
                    gap: 2
                }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                            Showing {startIndex + 1}-{Math.min(endIndex, jobs.length)} of {jobs.length} jobs
                        </Typography>

                        <FormControl size="small" sx={{ minWidth: 120 }}>
                            <InputLabel id="jobs-per-page-label">Per page</InputLabel>
                            <Select
                                labelId="jobs-per-page-label"
                                value={jobsPerPage}
                                label="Per page"
                                onChange={handleJobsPerPageChange}
                            >
                                <MenuItem value={5}>5</MenuItem>
                                <MenuItem value={10}>10</MenuItem>
                                <MenuItem value={25}>25</MenuItem>
                                <MenuItem value={50}>50</MenuItem>
                                <MenuItem value={100}>100</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>

                    {totalPages > 1 && (
                        <Pagination
                            count={totalPages}
                            page={currentPage}
                            onChange={handlePageChange}
                            color="primary"
                            showFirstButton
                            showLastButton
                            sx={{
                                '& .MuiPagination-ul': {
                                    justifyContent: 'center'
                                }
                            }}
                        />
                    )}
                </Box>
            )}

            {/* Job Details Dialog */}
            <Dialog
                open={dialogOpen}
                onClose={() => setDialogOpen(false)}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle>Job Details</DialogTitle>
                <DialogContent>
                    {selectedJob && (
                        <Box>
                            <Typography variant="h6" gutterBottom>
                                {selectedJob.user_name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                                Status: {getStatusText(selectedJob.status)} ({selectedJob.progress_percentage.toFixed(1)}%)
                            </Typography>
                            {selectedJob.error_message && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {selectedJob.error_message}
                                </Alert>
                            )}
                            {selectedJob.outline_content && (
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="subtitle1">Outline:</Typography>
                                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', maxHeight: 200, overflow: 'auto' }}>
                                        {selectedJob.outline_content}
                                    </Typography>
                                </Box>
                            )}
                            {selectedJob.final_biography_content && (
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="subtitle1">Final Biography (Preview):</Typography>
                                    <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', maxHeight: 200, overflow: 'auto' }}>
                                        {selectedJob.final_biography_content.substring(0, 500)}...
                                    </Typography>
                                </Box>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setDialogOpen(false)}>Close</Button>
                </DialogActions>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={deleteDialogOpen}
                onClose={() => setDeleteDialogOpen(false)}
            >
                <DialogTitle>Confirm Deletion</DialogTitle>
                <DialogContent>
                    <Typography>
                        Are you sure you want to delete this job? This action cannot be undone.
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleDelete} color="error" disabled={loading}>
                        Delete
                    </Button>
                </DialogActions>
            </Dialog>
        </Paper>
    );
};

export default JobList; 