#!/usr/bin/env python3
"""
Progressive Biography Service for large interview processing.
Handles step-by-step generation with buffered saving and resume capability.
"""

import asyncio
import json
import re
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.biography_job import BiographyJob, JobStatus
from app.services.pdf_service import PDFService
from app.services.ai_service import AIService
from app.services.websocket_manager import manager


class ProgressiveBiographyService:
    """Service for progressive biography generation with buffered saving."""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        
        # Configuration for large files
        self.max_segment_size = 8000  # Reduced for better token management
        self.segment_overlap = 300
        self.iterations_per_chapter = 5  # Reduced for faster processing
        self.delay_between_chapters = 2.0  # Longer delays for rate limiting
        self.max_retries = 3
        
    async def process_progressive_biography(self, job_id: str) -> bool:
        """Process biography with progressive generation and buffered saving."""
        print(f"🚀 Starting progressive biography generation for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        start_time = time.time()
        
        try:
            # Phase 1: Analysis and Preparation
            await self._phase_1_analysis(job)
            
            # Phase 2: Outline Generation  
            await self._phase_2_outline(job)
            
            # Phase 3: Progressive Chapter Generation
            await self._phase_3_progressive_chapters(job)
            
            # Phase 4: Final Assembly
            await self._phase_4_final_assembly(job)
            
            # Phase 5: PDF Generation
            await self._phase_5_pdf_generation(job)
            
            # Mark as completed
            job.processing_time_seconds = time.time() - start_time
            job.completed_at = datetime.utcnow()
            await self._update_job(job, JobStatus.COMPLETED, 100.0)
            
            print(f"✅ Progressive biography generation completed in {job.processing_time_seconds:.2f}s")
            return True
            
        except Exception as e:
            job.error_message = str(e)
            job.processing_time_seconds = time.time() - start_time
            await self._update_job(job, JobStatus.FAILED, job.progress_percentage)
            print(f"❌ Progressive biography generation failed: {e}")
            return False
    
    async def _phase_1_analysis(self, job: BiographyJob):
        """Phase 1: Analyze interview and extract metadata."""
        print("📊 Phase 1: Analysis and Preparation")
        await self._update_job(job, JobStatus.PROCESSING, 5.0)
        
        # Extract interview text
        interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
        if not interview_text.strip():
            raise Exception("Could not extract text from uploaded PDF")
        
        # Extract person name with improved logic
        extracted_name = self._extract_improved_name(job.uploaded_pdf_path, interview_text)
        
        # Analyze interview structure
        analysis = self._analyze_interview_structure(interview_text)
        
        # Store analysis results in job metadata
        job_metadata = {
            "extracted_name": extracted_name,
            "analysis": analysis,
            "interview_length": len(interview_text),
            "processing_strategy": "progressive" if analysis['needs_segmentation'] else "standard"
        }
        
        # Store in job (using a JSON field or create a new field)
        if not hasattr(job, 'metadata'):
            job.evaluation_content = json.dumps(job_metadata)  # Reuse evaluation_content for metadata
        
        self.db.commit()
        print(f"✅ Analysis complete: {analysis['word_count']:,} words, {len(analysis['segments'])} segments")
        await self._update_job(job, JobStatus.PROCESSING, 15.0)
    
    async def _phase_2_outline(self, job: BiographyJob):
        """Phase 2: Generate comprehensive outline."""
        print("📋 Phase 2: Outline Generation")
        
        # Get stored metadata
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        analysis = metadata.get('analysis', {})
        
        # Regenerate if needed
        if not analysis:
            interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
            analysis = self._analyze_interview_structure(interview_text)
        
        # Generate outline with retry logic
        outline_data = None
        for attempt in range(self.max_retries):
            try:
                outline_data = await self._generate_robust_outline(job.uploaded_pdf_path, analysis)
                break
            except Exception as e:
                print(f"⚠️ Outline attempt {attempt + 1} failed: {e}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(5)  # Wait before retry
        
        # Store outline
        job.outline_content = outline_data["outline"]
        job.total_chapters = outline_data["chapter_count"]
        job.completed_chapters = 0
        job.chapters_content = {}  # Initialize empty chapters
        
        self.db.commit()
        print(f"✅ Outline generated: {job.total_chapters} chapters planned")
        await self._update_job(job, JobStatus.OUTLINE_COMPLETE, 25.0)
        
        return outline_data
    
    async def _phase_3_progressive_chapters(self, job: BiographyJob):
        """Phase 3: Generate chapters progressively with buffered saving."""
        print("📝 Phase 3: Progressive Chapter Generation")
        await self._update_job(job, JobStatus.ITERATIVE_WRITING, 30.0)
        
        # Get outline and analysis
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        analysis = metadata.get('analysis', {})
        
        # Regenerate outline if needed
        if not job.outline_content:
            outline_data = await self._phase_2_outline(job)
        else:
            outline_data = self._reconstruct_outline_from_job(job)
        
        # Get interview text and segments
        interview_text = PDFService.extract_text_from_pdf(job.uploaded_pdf_path)
        if not analysis:
            analysis = self._analyze_interview_structure(interview_text)
        
        chapters_content = job.chapters_content or {}
        
        # Generate each chapter with progressive saving
        for i, chapter in enumerate(outline_data["chapters"]):
            chapter_num = chapter['number']
            chapter_key = f"chapter_{chapter_num}"
            
            # Skip if chapter already exists (resume capability)
            if chapter_key in chapters_content:
                print(f"⏭️ Chapter {chapter_num} already exists, skipping")
                continue
            
            print(f"\n🖋️ Generating Chapter {chapter_num}/{job.total_chapters}: {chapter['title']}")
            
            # Generate chapter with retries
            chapter_content = None
            for attempt in range(self.max_retries):
                try:
                    chapter_content = await self._generate_single_chapter_robust(
                        analysis['segments'], chapter, interview_text, chapters_content
                    )
                    break
                except Exception as e:
                    print(f"⚠️ Chapter {chapter_num} attempt {attempt + 1} failed: {e}")
                    if attempt == self.max_retries - 1:
                        # Use fallback content
                        chapter_content = f"Chapter {chapter_num}: {chapter['title']}\n\n[Content generation failed, using placeholder.]"
                    await asyncio.sleep(5)
            
            # Save chapter immediately (buffered saving)
            chapters_content[chapter_key] = {
                "title": chapter["title"],
                "content": chapter_content,
                "number": chapter_num,
                "generated_at": datetime.utcnow().isoformat(),
                "word_count": len(chapter_content.split())
            }
            
            job.chapters_content = chapters_content
            job.completed_chapters = i + 1
            self.db.commit()  # Save after each chapter
            
            print(f"✅ Chapter {chapter_num} saved ({len(chapter_content):,} chars)")
            
            # Update progress
            chapter_progress = 30.0 + ((i + 1) / job.total_chapters) * 50.0
            await self._update_job(job, JobStatus.ITERATIVE_WRITING, chapter_progress)
            
            # Rate limiting delay
            await asyncio.sleep(self.delay_between_chapters)
        
        print(f"✅ All {job.total_chapters} chapters generated and saved")
        await self._update_job(job, JobStatus.WRITING_COMPLETE, 80.0)
    
    async def _phase_4_final_assembly(self, job: BiographyJob):
        """Phase 4: Assemble final biography from buffered chapters."""
        print("📖 Phase 4: Final Assembly")
        
        if not job.chapters_content:
            raise Exception("No chapters content found for assembly")
        
        # Combine chapters with improved logic
        combined_biography = self._assemble_final_biography(job.chapters_content)
        
        # Store final biography
        job.biography_content = combined_biography
        job.final_biography_content = combined_biography
        
        # Add assembly metadata
        metadata = json.loads(job.evaluation_content) if job.evaluation_content else {}
        metadata.update({
            "final_assembly_at": datetime.utcnow().isoformat(),
            "final_biography_length": len(combined_biography),
            "final_word_count": len(combined_biography.split()),
            "chapters_assembled": len(job.chapters_content)
        })
        job.evaluation_content = json.dumps(metadata)
        
        self.db.commit()
        print(f"✅ Final biography assembled: {len(combined_biography):,} characters")
        await self._update_job(job, JobStatus.REWRITE_COMPLETE, 90.0)
    
    async def _phase_5_pdf_generation(self, job: BiographyJob):
        """Phase 5: Generate final PDF."""
        print("📄 Phase 5: PDF Generation")
        await self._update_job(job, JobStatus.GENERATING_PDF, 95.0)
        
        # Always use user_name from the form instead of extracted name
        person_name = job.user_name
        
        # Reconstruct chapters for PDF
        outline_data = self._reconstruct_outline_from_job(job)
        
        # Generate PDF
        output_filename = f"biography_{job.id}_{int(time.time())}.pdf"
        output_path = f"./output/{output_filename}"
        
        success = PDFService.generate_enhanced_biography_pdf(
            job.final_biography_content,
            person_name,
            outline_data["chapters"],
            output_path
        )
        
        if success:
            job.output_pdf_path = output_path
            print(f"✅ PDF generated: {output_path}")
        else:
            raise Exception("Failed to generate PDF")
    
    def _extract_improved_name(self, pdf_path: str, interview_text: str) -> str:
        """Extract person name with improved logic for AI interview filtering."""
        try:
            # Enhanced patterns that exclude AI names
            excluded_patterns = [
                r'alfred\s+from\s+eternal',
                r'eternal\s+ai',
                r'ai\s+assistant',
                r'chatgpt',
                r'assistant'
            ]
            
            # Look for user introductions
            user_intro_patterns = [
                r'user:\s*(?:my name is |i am |i\'m |this is )?([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'user:\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*here',
                r'hello.*?i\'m\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            ]
            
            text_lower = interview_text.lower()
            
            # Try user introduction patterns first
            for pattern in user_intro_patterns:
                matches = re.findall(pattern, interview_text, re.IGNORECASE)
                for match in matches:
                    # Skip if it's an excluded AI name
                    if not any(re.search(excl, match.lower()) for excl in excluded_patterns):
                        if len(match.split()) <= 3:  # Reasonable name length
                            return match.strip()
            
            # Fallback to filename extraction
            import os
            filename = os.path.basename(pdf_path)
            name_from_filename = re.search(r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', filename)
            if name_from_filename:
                potential_name = name_from_filename.group(1)
                excluded_words = ['interview', 'transcript', 'session', 'eternal', 'document']
                if potential_name.lower() not in excluded_words:
                    return potential_name
            
            return "Unknown Person"
            
        except Exception as e:
            print(f"⚠️ Name extraction error: {e}")
            return "Unknown Person"
    
    def _analyze_interview_structure(self, interview_text: str) -> Dict:
        """Analyze interview structure for processing strategy."""
        text_length = len(interview_text)
        word_count = len(interview_text.split())
        
        # Detect session breaks and create segments
        session_markers = []
        lines = interview_text.split('\n')
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            if any(marker in line_clean for marker in [
                'session', 'ai:', 'user:', 'eternal ai', 'welcome back',
                '--- page', 'chapter', 'part'
            ]):
                session_markers.append({
                    'line_number': i,
                    'content': line.strip(),
                    'marker_type': 'session_break' if 'session' in line_clean else 'dialogue'
                })
        
        # Determine segmentation strategy
        needs_segmentation = text_length > self.max_segment_size
        
        if needs_segmentation:
            segments = self._create_smart_segments(interview_text, session_markers)
        else:
            segments = [{'start': 0, 'end': text_length, 'content': interview_text}]
        
        return {
            'total_length': text_length,
            'word_count': word_count,
            'needs_segmentation': needs_segmentation,
            'session_markers': session_markers,
            'segments': segments,
            'estimated_biography_pages': min(max(word_count // 250, 50), 500)
        }
    
    def _create_smart_segments(self, text: str, session_markers: List[Dict]) -> List[Dict]:
        """Create intelligent segments based on content structure."""
        segments = []
        
        if not session_markers:
            # Fallback to character-based segmentation
            for start in range(0, len(text), self.max_segment_size - self.segment_overlap):
                end = min(start + self.max_segment_size, len(text))
                segments.append({
                    'start': start,
                    'end': end,
                    'content': text[start:end],
                    'type': 'character_based'
                })
        else:
            # Content-aware segmentation
            current_start = 0
            
            for i, marker in enumerate(session_markers):
                # Find natural break point
                marker_pos = text.find(marker['content'])
                if marker_pos > current_start + self.max_segment_size:
                    # Create segment before this marker
                    segments.append({
                        'start': current_start,
                        'end': marker_pos,
                        'content': text[current_start:marker_pos],
                        'type': 'content_aware',
                        'end_marker': marker['content']
                    })
                    current_start = marker_pos
            
            # Add final segment
            if current_start < len(text):
                segments.append({
                    'start': current_start,
                    'end': len(text),
                    'content': text[current_start:],
                    'type': 'final_segment'
                })
        
        return segments
    
    async def _generate_robust_outline(self, pdf_path: str, analysis: Dict) -> Dict:
        """Generate outline with robust error handling."""
        interview_text = PDFService.extract_text_from_pdf(pdf_path)
        
        # Create summary for outline generation
        if analysis['needs_segmentation']:
            # Use first few segments for outline
            summary_segments = analysis['segments'][:3]
            interview_summary = '\n\n'.join([seg['content'][:2000] for seg in summary_segments])
        else:
            interview_summary = interview_text[:10000]
        
        # Calculate recommended chapters
        estimated_pages = analysis['estimated_biography_pages']
        recommended_chapters = min(max(estimated_pages // 35, 6), 10)  # 6-10 chapters for robustness
        
        # AI outline generation with fallback
        try:
            system_message = f"""You are an expert biography outliner creating a {recommended_chapters}-chapter structure.
            
            CRITICAL: Respond with ONLY valid JSON in this exact format:
            {{
                "outline": "brief description",
                "chapters": [
                    {{
                        "number": 1,
                        "title": "Chapter Title",
                        "description": "What this chapter covers",
                        "key_themes": ["theme1", "theme2"],
                        "estimated_sections": 3,
                        "time_period": "childhood/early years/etc"
                    }}
                ],
                "chapter_count": {recommended_chapters}
            }}"""
            
            prompt = f"""
Create a {recommended_chapters}-chapter biography outline based on this interview content:

{interview_summary[:5000]}

Generate a comprehensive chapter structure with themes and time periods.
Respond with ONLY the JSON format specified.
"""
            
            response = await self.ai_service.generate_completion(
                prompt, system_message, max_tokens=2000
            )
            
            # Log AI response before parsing
            print(f"\n🤖 [OUTLINE DEBUG] AI response received for robust outline generation:")
            print(f"📏 [OUTLINE DEBUG] Response length: {len(response)} chars")
            print(f"📄 [OUTLINE DEBUG] Response preview: {response[:300]}...")
            
            # Try to parse JSON with better error handling
            import re
            
            # Clean response and find JSON
            response = response.strip()
            print(f"🔍 [OUTLINE DEBUG] Starting JSON parsing for robust outline...")
            
            # Try multiple patterns to find JSON
            json_patterns = [
                (r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', "Simple nested JSON"),
                (r'\{.*?\}', "Basic JSON match"), 
                (r'```json\s*(\{.*?\})\s*```', "Markdown JSON block"),
                (r'```\s*(\{.*?\})\s*```', "Any code block")
            ]
            
            json_content = None
            for pattern, pattern_name in json_patterns:
                print(f"🔎 [OUTLINE DEBUG] Trying pattern: {pattern_name}")
                matches = re.findall(pattern, response, re.DOTALL)
                if matches:
                    json_content = matches[0] if isinstance(matches[0], str) else matches[0]
                    print(f"✅ [OUTLINE DEBUG] Found JSON with {pattern_name}")
                    print(f"📄 [OUTLINE DEBUG] JSON preview: {json_content[:200]}...")
                    break
                else:
                    print(f"❌ [OUTLINE DEBUG] No matches for {pattern_name}")
            
            if json_content:
                try:
                    outline_data = json.loads(json_content)
                    print(f"✅ [OUTLINE DEBUG] Successfully parsed JSON")
                    print(f"📊 [OUTLINE DEBUG] Parsed data keys: {list(outline_data.keys())}")
                    if 'chapters' in outline_data:
                        print(f"📚 [OUTLINE DEBUG] Found {len(outline_data['chapters'])} chapters")
                    return outline_data
                except json.JSONDecodeError as e:
                    print(f"❌ [OUTLINE DEBUG] JSON parse error: {e}")
                    print(f"📄 [OUTLINE DEBUG] Raw JSON content: {json_content[:200]}...")
                    print(f"📄 [OUTLINE DEBUG] Full failed JSON:")
                    print(f"---START---")
                    print(json_content)
                    print(f"---END---")
                    raise Exception(f"Invalid JSON in AI response: {e}")
            else:
                print(f"❌ [OUTLINE DEBUG] No JSON found in AI response!")
                print(f"📄 [OUTLINE DEBUG] Full AI response:")
                print(f"---START---")
                print(response)
                print(f"---END---")
                raise Exception("No valid JSON found in response")
                
        except Exception as e:
            print(f"⚠️ AI outline failed, using fallback: {e}")
            return self._create_fallback_outline(recommended_chapters)
    
    def _create_fallback_outline(self, chapter_count: int) -> Dict:
        """Create fallback outline structure."""
        chapter_themes = [
            ("Early Life and Origins", ["family", "childhood", "upbringing"], "early years"),
            ("Growing Up", ["education", "development", "youth"], "childhood"),
            ("Coming of Age", ["adolescence", "identity", "growth"], "teenage years"),
            ("Education and Learning", ["school", "knowledge", "skills"], "educational years"),
            ("Career Beginnings", ["work", "profession", "career"], "early career"),
            ("Personal Relationships", ["family", "friends", "love"], "relationships"),
            ("Professional Growth", ["career", "success", "challenges"], "career development"),
            ("Life Challenges", ["obstacles", "struggles", "perseverance"], "difficult times"),
            ("Achievements", ["success", "accomplishments", "recognition"], "peak years"),
            ("Wisdom and Reflection", ["lessons", "advice", "legacy"], "later years")
        ]
        
        chapters = []
        for i in range(min(chapter_count, len(chapter_themes))):
            title, themes, period = chapter_themes[i]
            chapters.append({
                "number": i + 1,
                "title": title,
                "description": f"Explores {title.lower()} and related experiences",
                "key_themes": themes,
                "estimated_sections": 3,
                "time_period": period
            })
        
        return {
            "outline": f"A comprehensive {chapter_count}-chapter biography covering major life periods and themes",
            "chapters": chapters,
            "chapter_count": chapter_count
        }
    
    async def _generate_single_chapter_robust(
        self, segments: List[Dict], chapter: Dict, interview_text: str, existing_chapters: Dict
    ) -> str:
        """Generate a single chapter with robust error handling."""
        
        # Find relevant segments
        relevant_segments = self._find_relevant_segments_for_chapter(segments, chapter)
        
        # Prepare context from existing chapters
        context = ""
        if existing_chapters:
            last_chapters = list(existing_chapters.values())[-2:]  # Last 2 chapters for context
            context = "\n\n".join([ch['content'][-500:] for ch in last_chapters])
        
        # Generate with reduced iterations for speed
        current_content = ""
        for iteration in range(self.iterations_per_chapter):
            
            # Prepare prompt
            segment_context = self._format_segments_for_prompt(relevant_segments[:3])  # Top 3 segments
            
            prompt = f"""
Write Chapter {chapter['number']}: {chapter['title']}

Description: {chapter['description']}
Key Themes: {', '.join(chapter['key_themes'][:3])}
Time Period: {chapter.get('time_period', 'Not specified')}

Relevant Interview Content:
{segment_context[:3000]}

Previous Chapters Context:
{context[-1000:] if context else "This is the first chapter."}

Current Chapter Content:
{current_content[-1000:] if current_content else "Starting new chapter."}

Write a compelling biographical chapter (1500-2500 words) that flows naturally and tells a complete story for this life period.
"""
            
            current_content = await self.ai_service.generate_completion(
                prompt,
                f"You are writing Chapter {chapter['number']} of a comprehensive biography.",
                max_tokens=2000
            )
            
            # Short delay between iterations
            if iteration < self.iterations_per_chapter - 1:
                await asyncio.sleep(1)
        
        return current_content
    
    def _find_relevant_segments_for_chapter(self, segments: List[Dict], chapter: Dict) -> List[Dict]:
        """Find segments most relevant to a chapter."""
        relevant_segments = []
        chapter_keywords = chapter['key_themes'] + [chapter.get('time_period', '')]
        
        for segment in segments:
            relevance_score = 0
            segment_lower = segment['content'].lower()
            
            # Score based on keyword matches
            for keyword in chapter_keywords:
                if keyword and keyword.lower() in segment_lower:
                    relevance_score += 1
            
            # Add time-period specific scoring
            time_period = chapter.get('time_period', '')
            if time_period:
                time_keywords = {
                    'childhood': ['child', 'young', 'kid', 'school'],
                    'teenage': ['teen', 'high school', 'adolescent'],
                    'early career': ['first job', 'started work', 'career'],
                    'later years': ['older', 'retired', 'grandchild', 'wisdom']
                }
                
                for keyword in time_keywords.get(time_period, []):
                    if keyword in segment_lower:
                        relevance_score += 2
            
            if relevance_score > 0:
                segment['relevance_score'] = relevance_score
                relevant_segments.append(segment)
        
        # Sort by relevance and return top segments
        relevant_segments.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return relevant_segments[:5]
    
    def _format_segments_for_prompt(self, segments: List[Dict]) -> str:
        """Format segments for AI prompts."""
        formatted = []
        for i, segment in enumerate(segments):
            content = segment['content'][:1500]  # Limit segment size
            if len(segment['content']) > 1500:
                content += "..."
            formatted.append(f"--- Segment {i+1} ---\n{content}")
        
        return "\n\n".join(formatted)
    
    def _assemble_final_biography(self, chapters_content: Dict) -> str:
        """Assemble final biography from chapter contents."""
        combined = ""
        
        # Sort chapters by number
        sorted_chapters = sorted(
            chapters_content.items(),
            key=lambda x: x[1]["number"]
        )
        
        for chapter_key, chapter_data in sorted_chapters:
            chapter_title = chapter_data['title']
            chapter_number = chapter_data['number']
            
            # Improved title handling - avoid duplication
            if not chapter_title.startswith(f"Chapter {chapter_number}"):
                combined += f"\n\nChapter {chapter_number}: {chapter_title}\n\n"
            else:
                combined += f"\n\n{chapter_title}\n\n"
            
            combined += chapter_data["content"]
            combined += "\n" + "="*80 + "\n"
        
        return combined
    
    def _reconstruct_outline_from_job(self, job: BiographyJob) -> Dict:
        """Reconstruct outline data from job."""
        chapters = []
        
        if job.chapters_content:
            # Use actual chapter data
            for key, chapter_data in job.chapters_content.items():
                chapters.append({
                    "number": chapter_data["number"],
                    "title": chapter_data["title"],
                    "description": f"Chapter about {chapter_data['title'].lower()}",
                    "key_themes": ["life_experience"],
                    "estimated_sections": 3
                })
        else:
            # Create fallback chapters
            for i in range(1, job.total_chapters + 1):
                chapters.append({
                    "number": i,
                    "title": f"Chapter {i}",
                    "description": f"Chapter {i} content",
                    "key_themes": ["life_experience"],
                    "estimated_sections": 3
                })
        
        return {
            "outline": job.outline_content or "Generated biography outline",
            "chapters": chapters,
            "chapter_count": job.total_chapters
        }
    
    async def _update_job(self, job: BiographyJob, status: JobStatus, progress: float):
        """Update job status and progress."""
        job.status = status
        job.progress_percentage = progress
        job.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Send WebSocket update
        try:
            await manager.broadcast_job_update(job.id)
        except Exception as e:
            print(f"⚠️ WebSocket update failed: {e}")
    
    async def resume_progressive_biography(self, job_id: str) -> bool:
        """Resume progressive biography generation from where it left off."""
        print(f"🔄 Resuming progressive biography generation for job {job_id}")
        
        job = self.db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            raise Exception(f"Job {job_id} not found")
        
        # Determine which phase to resume from
        if job.status == JobStatus.FAILED or job.progress_percentage < 15:
            return await self.process_progressive_biography(job_id)
        elif job.progress_percentage < 25:
            await self._phase_2_outline(job)
            await self._phase_3_progressive_chapters(job)
            await self._phase_4_final_assembly(job)
            await self._phase_5_pdf_generation(job)
        elif job.progress_percentage < 80:
            await self._phase_3_progressive_chapters(job)
            await self._phase_4_final_assembly(job)
            await self._phase_5_pdf_generation(job)
        elif job.progress_percentage < 90:
            await self._phase_4_final_assembly(job)
            await self._phase_5_pdf_generation(job)
        elif job.progress_percentage < 100:
            await self._phase_5_pdf_generation(job)
        
        job.completed_at = datetime.utcnow()
        await self._update_job(job, JobStatus.COMPLETED, 100.0)
        return True