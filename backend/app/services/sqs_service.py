import boto3
import json
import uuid
from typing import Dict, Optional, Any
from datetime import datetime, timezone
import asyncio
import logging
from botocore.exceptions import ClientError

from app.core.config import settings

logger = logging.getLogger(__name__)

class SQSService:
    """Service for managing SQS queues and messages for background job processing."""
    
    def __init__(self):
        self.sqs_client = boto3.client(
            'sqs',
            region_name=settings.aws_region,
            aws_access_key_id=settings.aws_access_key_id,
            aws_secret_access_key=settings.aws_secret_access_key
        )
        self.queue_url = settings.sqs_queue_url
        self.dlq_url = settings.sqs_dlq_queue_url
        
    async def send_biography_job(self, job_id: str, use_iterative: bool = True) -> bool:
        """Send a biography job to SQS queue for processing.
        
        Args:
            job_id: The biography job ID to process
            use_iterative: Whether to use iterative processing (True for book-length content)
            
        Returns:
            bool: True if message was sent successfully
        """
        try:
            message_body = {
                "job_type": "biography_generation",
                "job_id": job_id,
                "use_iterative": use_iterative,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "retry_count": 0
            }
            
            # Send message to SQS
            send_params = {
                'QueueUrl': self.queue_url,
                'MessageBody': json.dumps(message_body),
                'MessageAttributes': {
                    'JobType': {
                        'StringValue': 'biography_generation',
                        'DataType': 'String'
                    },
                    'JobId': {
                        'StringValue': job_id,
                        'DataType': 'String'
                    }
                }
            }
            
            # Add FIFO-specific parameters if using FIFO queue
            if '.fifo' in self.queue_url:
                send_params['MessageGroupId'] = f"biography-{job_id}"
                # Always provide MessageDeduplicationId for FIFO queues
                send_params['MessageDeduplicationId'] = f"job-{job_id}-{int(datetime.now(timezone.utc).timestamp())}"
            
            response = self.sqs_client.send_message(**send_params)
            
            logger.info(f"Biography job {job_id} sent to SQS. MessageId: {response['MessageId']}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to send biography job {job_id} to SQS: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending job {job_id} to SQS: {e}")
            return False
    
    async def receive_messages(self, max_messages: int = 1, wait_time: int = 20) -> list:
        """Receive messages from SQS queue for processing.
        
        Args:
            max_messages: Maximum number of messages to receive (1-10)
            wait_time: Long polling wait time in seconds (0-20)
            
        Returns:
            list: List of received messages
        """
        try:
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=min(max_messages, 10),
                WaitTimeSeconds=min(wait_time, 20),
                MessageAttributeNames=['All'],
                AttributeNames=['All']
            )
            
            messages = response.get('Messages', [])
            logger.info(f"Received {len(messages)} messages from SQS")
            return messages
            
        except ClientError as e:
            logger.error(f"Failed to receive messages from SQS: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error receiving messages from SQS: {e}")
            return []
    
    async def delete_message(self, receipt_handle: str) -> bool:
        """Delete a processed message from SQS queue.
        
        Args:
            receipt_handle: Receipt handle of the message to delete
            
        Returns:
            bool: True if message was deleted successfully
        """
        try:
            self.sqs_client.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle
            )
            logger.info("Message deleted from SQS successfully")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete message from SQS: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting message from SQS: {e}")
            return False
    
    async def send_to_dlq(self, original_message: Dict, error_reason: str) -> bool:
        """Send a failed message to Dead Letter Queue.
        
        Args:
            original_message: The original message that failed processing
            error_reason: Reason for the failure
            
        Returns:
            bool: True if message was sent to DLQ successfully
        """
        if not self.dlq_url:
            logger.warning("DLQ not configured, cannot send failed message")
            return False
            
        try:
            dlq_message = {
                **original_message,
                "error_reason": error_reason,
                "failed_at": datetime.now(timezone.utc).isoformat(),
                "dlq_timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            send_params = {
                'QueueUrl': self.dlq_url,
                'MessageBody': json.dumps(dlq_message)
            }
            
            # Add FIFO-specific parameters if using FIFO DLQ
            if '.fifo' in self.dlq_url:
                send_params['MessageGroupId'] = f"dlq-{original_message.get('job_id', 'unknown')}"
                send_params['MessageDeduplicationId'] = str(uuid.uuid4())
            
            response = self.sqs_client.send_message(**send_params)
            
            logger.info(f"Failed message sent to DLQ. MessageId: {response['MessageId']}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to send message to DLQ: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending message to DLQ: {e}")
            return False
    
    async def process_message(self, message: Dict) -> bool:
        """Process a single SQS message using intelligent biography processing.
        
        Args:
            message: SQS message to process
            
        Returns:
            bool: True if message was processed successfully
        """
        try:
            # Parse message body
            body = json.loads(message['Body'])
            job_type = body.get('job_type')
            job_id = body.get('job_id')
            
            if job_type != 'biography_generation':
                logger.error(f"Unknown job type: {job_type}")
                return False
            
            if not job_id:
                logger.error("No job_id found in message")
                return False
            
            # Import here to avoid circular imports
            from app.services.biography_service import BiographyService
            from app.models.base import SessionLocal
            
            # Process the biography job with intelligent strategy selection
            with SessionLocal() as db:
                biography_service = BiographyService(db)
                
                # Use the new intelligent processing method
                success = await biography_service.process_biography_intelligently(job_id)
                
                if success:
                    logger.info(f"✅ Successfully processed biography job {job_id} with intelligent processing")
                    return True
                else:
                    logger.error(f"❌ Failed to process biography job {job_id}")
                    return False
                    
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse message body: {e}")
            return False
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return False
    
    async def get_queue_attributes(self) -> Dict[str, Any]:
        """Get queue attributes for monitoring.
        
        Returns:
            Dict: Queue attributes including message counts
        """
        try:
            response = self.sqs_client.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=[
                    'ApproximateNumberOfMessages',
                    'ApproximateNumberOfMessagesNotVisible',
                    'ApproximateNumberOfMessagesDelayed'
                ]
            )
            return response.get('Attributes', {})
            
        except ClientError as e:
            logger.error(f"Failed to get queue attributes: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error getting queue attributes: {e}")
            return {}

# Global SQS service instance
sqs_service = SQSService() 