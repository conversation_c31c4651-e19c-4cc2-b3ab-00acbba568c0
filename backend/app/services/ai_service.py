from typing import Optional
import asyncio
import openai
import anthropic
import os
from app.core.config import settings
import time
from sqlalchemy.orm import Session
from app.services.prompt_service import PromptService
from app.models.app_settings import AppSettings

class AIService:
    def __init__(self, db: Session = None):
        self.provider = settings.ai_provider
        self.db = db
        self.prompt_service = PromptService(db) if db else None
        
        # Force settings to take precedence over environment variables
        # Clear any environment variables that might interfere
        if 'OPENAI_API_KEY' in os.environ:
            print(f"🔧 AI Service: Removing OPENAI_API_KEY environment variable to use .env settings")
            del os.environ['OPENAI_API_KEY']
        
        if 'ANTHROPIC_API_KEY' in os.environ:
            print(f"🔧 AI Service: Removing ANTHROPIC_API_KEY environment variable to use .env settings")
            del os.environ['ANTHROPIC_API_KEY']
        
        if self.provider == "openai" and settings.openai_api_key:
            self.client = openai.OpenAI(api_key=settings.openai_api_key)
            print(f"🔑 AI Service: Using OpenAI API key from .env file (ending with: ...{settings.openai_api_key[-6:]})")
        elif self.provider == "anthropic" and settings.anthropic_api_key:
            self.client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
            print(f"🔑 AI Service: Using Anthropic API key from .env file (ending with: ...{settings.anthropic_api_key[-6:]})")
        else:
            print(f"❌ AI Service: No valid API key found for provider: {self.provider}")
            print(f"   OpenAI key present: {bool(settings.openai_api_key)}")
            print(f"   Anthropic key present: {bool(settings.anthropic_api_key)}")
            raise ValueError(f"No valid API key found for provider: {self.provider}")
    
    def get_current_model(self) -> str:
        """Get the currently selected AI model from database settings."""
        if not self.db:
            print(f"⚠️ [AI DEBUG] No database session available, using default model")
            return self._get_default_model()
        
        try:
            # Get current model setting from database
            model_setting = self.db.query(AppSettings).filter(
                AppSettings.setting_key == "openai_model",
                AppSettings.is_active == True
            ).first()
            
            if model_setting:
                selected_model = model_setting.setting_value
                print(f"✅ [AI DEBUG] model ==> Using user-selected model from DB: {selected_model}")
                return selected_model
            else:
                def_model = self._get_default_model()
                print(f"⚠️ [AI DEBUG] model ==> No model setting found in DB, using default: {def_model}")
                return def_model
                
        except Exception as e:
            print(f"❌ [AI DEBUG] model ==> Error getting model from DB: {e}, using default")
            return self._get_default_model()
    
    def _get_default_model(self) -> str:
        """Get default model based on provider."""
        if self.provider == "openai":
            return "gpt-3.5-turbo"
        elif self.provider == "anthropic":
            return "claude-3-haiku-20240307"
        else:
            return "gpt-3.5-turbo"  # Fallback
    
    def _map_to_anthropic_model(self, model_name: str) -> str:
        """Map OpenAI model names to Anthropic equivalents when using Anthropic provider."""
        # If already an Anthropic model, return as-is
        if model_name.startswith("claude"):
            return model_name
        
        # Map OpenAI models to Anthropic equivalents based on capability level
        model_mapping = {
            "gpt-3.5-turbo": "claude-3-haiku-20240307",
            "gpt-3.5-turbo-16k": "claude-3-haiku-20240307",
            "gpt-4": "claude-3-sonnet-20240229", 
            "gpt-4-turbo-preview": "claude-3-sonnet-20240229",
            "gpt-4o": "claude-3-opus-20240229",
            "gpt-4o-mini": "claude-3-haiku-20240307"
        }
        
        mapped_model = model_mapping.get(model_name, "claude-3-haiku-20240307")
        if mapped_model != model_name:
            print(f"🔄 [AI DEBUG] Mapped OpenAI model '{model_name}' to Anthropic model '{mapped_model}'")
        
        return mapped_model
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation: 4 chars = 1 token)."""
        return len(text) // 4

    def truncate_to_token_limit(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit."""
        estimated_tokens = self.estimate_tokens(text)
        if estimated_tokens <= max_tokens:
            return text
        
        # Calculate approximate character limit
        char_limit = max_tokens * 4
        
        # Try to cut at sentence boundary
        if len(text) > char_limit:
            truncated = text[:char_limit]
            # Find last sentence ending
            last_period = truncated.rfind('.')
            last_newline = truncated.rfind('\n')
            
            if last_period > char_limit - 200:  # If sentence end is near the limit
                truncated = truncated[:last_period + 1]
            elif last_newline > char_limit - 200:  # If newline is near the limit
                truncated = truncated[:last_newline]
            
            return truncated + "\n\n[Content truncated to fit token limit]"
        
        return text

    def prepare_safe_prompt(self, prompt: str, system_message: str, max_total_tokens: int = 14000) -> tuple[str, str]:
        """Prepare prompt and system message to fit within token limits."""
        
        # Reserve tokens for system message and response
        system_tokens = self.estimate_tokens(system_message)
        response_tokens = 3000  # Reserve for response
        available_tokens = max_total_tokens - system_tokens - response_tokens
        
        # Truncate prompt if necessary
        safe_prompt = self.truncate_to_token_limit(prompt, available_tokens)
        
        return safe_prompt, system_message

    async def generate_completion(
        self, 
        prompt: str, 
        system_message: str, 
        max_tokens: int = 2000,
        temperature: float = 0.7,
        max_retries: int = 3
    ) -> str:
        """Generate completion with automatic prompt size management and optimized settings."""
        
        # Adjust temperature based on task type for better creativity and diversity
        if "biographer" in system_message.lower() or "writer" in system_message.lower():
            # Higher temperature for creative biographical writing
            temperature = max(temperature, 0.8)
        elif "iteration" in system_message.lower():
            # Even higher temperature for iterative content to avoid repetition
            temperature = max(temperature, 0.85)
        elif "thesis" in system_message.lower() or "summariz" in system_message.lower():
            # Lower temperature for precise summaries
            temperature = min(temperature, 0.6)
        
        # Prepare safe prompt
        safe_prompt, safe_system = self.prepare_safe_prompt(prompt, system_message)
        
        # Log token usage
        prompt_tokens = self.estimate_tokens(safe_prompt)
        system_tokens = self.estimate_tokens(safe_system)
        total_input_tokens = prompt_tokens + system_tokens
        
        print(f"🔢 Token usage: {total_input_tokens} input tokens (prompt: {prompt_tokens}, system: {system_tokens}), temp: {temperature:.2f}")
        
        if total_input_tokens > 14000:
            print(f"⚠️  Warning: Input tokens ({total_input_tokens}) approaching limit, consider further reduction")
        
        # Get the current model from database settings
        current_model = self.get_current_model()
        print(f"🤖 [AI DEBUG] Using model: {current_model} for generation")
        
        for attempt in range(max_retries):
            try:
                if self.provider == "openai":
                    response = self.client.chat.completions.create(
                        model=current_model,  # Use user-selected model from database
                        messages=[
                            {"role": "system", "content": safe_system},
                            {"role": "user", "content": safe_prompt}
                        ],
                        max_tokens=max_tokens,
                        temperature=temperature,
                        presence_penalty=0.3,  # Encourage new topics
                        frequency_penalty=0.2   # Discourage repetitive phrases
                    )
                    return response.choices[0].message.content
                    
                elif self.provider == "anthropic":
                    # Map OpenAI model names to Anthropic equivalents if needed
                    anthropic_model = self._map_to_anthropic_model(current_model)
                    response = self.client.messages.create(
                        model=anthropic_model,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        system=safe_system,
                        messages=[
                            {"role": "user", "content": safe_prompt}
                        ]
                    )
                    return response.content[0].text
                    
            except Exception as e:
                error_msg = str(e)
                
                # Check for context length errors
                if "context_length_exceeded" in error_msg or "maximum context length" in error_msg:
                    print(f"🚨 Context length exceeded on attempt {attempt + 1}, reducing prompt size...")
                    # Reduce prompt size more aggressively
                    safe_prompt = self.truncate_to_token_limit(safe_prompt, len(safe_prompt) // 2)
                    continue
                
                if attempt == max_retries - 1:
                    raise Exception(f"AI service error after {max_retries} attempts: {error_msg}")
                
                print(f"⚠️  Attempt {attempt + 1} failed: {error_msg}, retrying...")
                await asyncio.sleep(1 * (attempt + 1))  # Progressive backoff
        
        raise Exception("Failed to generate completion after all retries")
    
    async def process_outline_agent(self, interview_text: str, prompt: str = None) -> str:
        """Process the outline agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting outline agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading outline prompt from database...")
            prompt = self.prompt_service.get_prompt("outline")
            print(f"✅ [AI DEBUG] Loaded outline prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Create a structured outline for a biography based on the interview content."
            print(f"⚠️ [AI DEBUG] Using default outline prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided outline prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Outline prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Biography Outliner agent. Create a structured outline for a biography."
        
        # Truncate interview text to safe size for outline generation
        safe_interview_text = self.truncate_to_token_limit(interview_text, 8000)  # Conservative limit
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        
        full_prompt = f"""
{prompt}

=========== INTERVIEW TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

Please create the biography outline based on this interview transcript excerpt.
"""
        
        print(f"🚀 [AI DEBUG] Generating outline with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2000)
    
    async def process_writer_agent(self, interview_text: str, outline: str, prompt: str = None) -> str:
        """Process the writer agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting writer agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading writer prompt from database...")
            prompt = self.prompt_service.get_prompt("writer")
            print(f"✅ [AI DEBUG] Loaded writer prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Write a comprehensive, engaging biography based on the interview and outline provided."
            print(f"⚠️ [AI DEBUG] Using default writer prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided writer prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Writer prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Full Biography Writer agent. Write a comprehensive biography."
        
        # Truncate inputs to safe sizes
        safe_interview_text = self.truncate_to_token_limit(interview_text, 6000)
        safe_outline = self.truncate_to_token_limit(outline, 2000)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Outline truncated to {len(safe_outline)} chars (from {len(outline)})")
        
        full_prompt = f"""
{prompt[:1000]}  # Limit prompt size too

=========== INTERVIEW TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== BIOGRAPHY OUTLINE ===========
{safe_outline}

Please write the full biography based on the interview transcript excerpt and outline.
"""
        
        print(f"🚀 [AI DEBUG] Generating biography with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2500)
    
    async def process_evaluation_agent(self, interview_text: str, biography: str, prompt: str = None) -> str:
        """Process the evaluation agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting evaluation agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading evaluation prompt from database...")
            prompt = self.prompt_service.get_prompt("evaluation")
            print(f"✅ [AI DEBUG] Loaded evaluation prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Review and evaluate the biography for accuracy, style, and completeness."
            print(f"⚠️ [AI DEBUG] Using default evaluation prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided evaluation prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Evaluation prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Evaluation Agent. Review and grade the biography."
        
        # Truncate inputs to safe sizes  
        safe_interview_text = self.truncate_to_token_limit(interview_text, 4000)
        safe_biography = self.truncate_to_token_limit(biography, 4000)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Biography truncated to {len(safe_biography)} chars (from {len(biography)})")
        
        full_prompt = f"""
{prompt[:800]}  # Limit prompt size

=========== ORIGINAL TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== BIOGRAPHY TO EVALUATE ===========
{safe_biography}

Please evaluate the biography based on the criteria provided in the prompt.
"""
        
        print(f"🚀 [AI DEBUG] Generating evaluation with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2000)
    
    async def process_rewrite_agent(self, interview_text: str, biography: str, evaluation: str, prompt: str = None) -> str:
        """Process the rewrite agent step with token safety."""
        
        print(f"\n🤖 [AI DEBUG] Starting rewrite agent processing...")
        
        # Get prompt from database if not provided
        if not prompt and self.prompt_service:
            print(f"📋 [AI DEBUG] Loading rewrite prompt from database...")
            prompt = self.prompt_service.get_prompt("rewrite")
            print(f"✅ [AI DEBUG] Loaded rewrite prompt ({len(prompt)} chars)")
        elif not prompt:
            prompt = "Improve the biography based on the evaluation feedback provided."
            print(f"⚠️ [AI DEBUG] Using default rewrite prompt (no database service available)")
        else:
            print(f"📝 [AI DEBUG] Using provided rewrite prompt ({len(prompt)} chars)")
        
        # Log prompt preview for debugging
        print(f"📄 [AI DEBUG] Rewrite prompt preview: {prompt[:200]}...")
        
        system_message = "You are the Rewrite Agent. Improve the biography based on evaluation feedback."
        
        # Truncate inputs to safe sizes
        safe_interview_text = self.truncate_to_token_limit(interview_text, 3000)
        safe_biography = self.truncate_to_token_limit(biography, 4000)
        safe_evaluation = self.truncate_to_token_limit(evaluation, 1500)
        
        print(f"📏 [AI DEBUG] Interview text truncated to {len(safe_interview_text)} chars (from {len(interview_text)})")
        print(f"📏 [AI DEBUG] Biography truncated to {len(safe_biography)} chars (from {len(biography)})")
        print(f"📏 [AI DEBUG] Evaluation truncated to {len(safe_evaluation)} chars (from {len(evaluation)})")
        
        full_prompt = f"""
{prompt[:800]}  # Limit prompt size

=========== ORIGINAL TRANSCRIPT (EXCERPT) ===========
{safe_interview_text}

=========== CURRENT BIOGRAPHY ===========
{safe_biography}

=========== EVALUATION FEEDBACK ===========
{safe_evaluation}

Please rewrite and improve the biography based on the evaluation feedback.
"""
        
        print(f"🚀 [AI DEBUG] Generating rewrite with full prompt ({len(full_prompt)} chars)")
        return await self.generate_completion(full_prompt, system_message, max_tokens=2500) 