from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse, StreamingResponse, Response
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
import time
import logging
import json
import asyncio
from datetime import datetime
from fastapi import BackgroundTasks

from app.models.base import get_db, SessionLocal
from app.models.biography_job import BiographyJob, JobStatus
from app.schemas.biography_job import BiographyJobCreate, BiographyJobResponse
from app.services.pdf_service import PDFService
from app.services.biography_service import BiographyService
from app.services.websocket_manager import manager
from app.services.sqs_service import sqs_service
from app.core.config import settings

router = APIRouter(prefix="/api/biography", tags=["biography"])

logger = logging.getLogger(__name__)

@router.post("/upload", response_model=BiographyJobResponse)
async def upload_interview_pdf(
    user_name: str = Form(...),
    email: Optional[str] = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload an interview PDF and create a new biography generation job."""
    
    # Validate file type
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    # Check file size
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    if file_size > settings.max_file_size_mb * 1024 * 1024:
        raise HTTPException(status_code=400, detail=f"File size exceeds {settings.max_file_size_mb}MB limit")
    
    # Save uploaded file
    timestamp = int(time.time())
    filename = f"{timestamp}_{file.filename}"
    upload_path = os.path.join(settings.upload_directory, filename)
    
    with open(upload_path, "wb") as buffer:
        buffer.write(content)
    
    # Validate PDF file
    if not PDFService.validate_pdf_file(upload_path):
        os.remove(upload_path)
        raise HTTPException(status_code=400, detail="Invalid PDF file")
    
    # Create database record
    job = BiographyJob(
        user_name=user_name,
        email=email,
        uploaded_pdf_path=upload_path,
        status=JobStatus.PENDING
    )
    
    db.add(job)
    db.commit()
    db.refresh(job)
    
    return job

@router.post("/process/{job_id}")
async def start_processing(
    job_id: str, 
    db: Session = Depends(get_db)
):
    """Start intelligent biography processing using SQS queue.
    
    The system automatically selects the optimal processing strategy:
    - Progressive processing for large/complex files (Jennifer type files)
    - Standard iterative processing for normal files
    
    Args:
        job_id: The job ID to process
    """
    
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    if job.status != JobStatus.PENDING:
        raise HTTPException(status_code=400, detail="Job is not in pending status")

    # Update job status to processing
    job.status = JobStatus.PROCESSING
    job.progress_percentage = 0.0
    job.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(job)
    
    # Send WebSocket update after status change
    try:
        await manager.broadcast_job_update(job.id)
        logger.info(f"WebSocket update sent for job {job_id[:8]} - status changed to PROCESSING")
    except Exception as e:
        logger.error(f"Failed to send WebSocket update for job {job_id[:8]}: {e}")

    # Send job to SQS queue for intelligent processing
    logger.info(f"Sending biography job {job_id} to SQS queue with intelligent processing")
    success = await sqs_service.send_biography_job(job_id=job_id, use_iterative=True)
    
    if not success:
        # Revert job status if failed to queue
        job.status = JobStatus.PENDING
        db.commit()
        raise HTTPException(status_code=500, detail="Failed to queue job for processing")
    
    return {
        "message": "Biography processing queued with intelligent strategy selection", 
        "job_id": job_id, 
        "status": "queued",
        "processing_type": "intelligent (auto-selected strategy)"
    }

@router.post("/process/{job_id}/sequential")
async def start_sequential_processing(
    job_id: str, 
    db: Session = Depends(get_db),
    chapter_number: Optional[int] = None
):
    """Start sequential chapter processing for a biography generation job using SQS queue.
    
    Args:
        job_id: The job ID to process
        chapter_number: Specific chapter to generate (1-based). If None, starts from chapter 1.
    """
    
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    if job.status not in [JobStatus.PENDING, JobStatus.ITERATIVE_WRITING]:
        raise HTTPException(status_code=400, detail="Job is not in appropriate status for sequential processing")

    # Update job status to iterative writing
    job.status = JobStatus.ITERATIVE_WRITING
    job.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(job)
    
    # Send WebSocket update after status change
    try:
        await manager.broadcast_job_update(job.id)
        logger.info(f"WebSocket update sent for job {job_id[:8]} - status changed to ITERATIVE_WRITING")
    except Exception as e:
        logger.error(f"Failed to send WebSocket update for job {job_id[:8]}: {e}")

    # Send sequential job to SQS queue
    logger.info(f"Sending sequential biography job {job_id} to SQS queue, chapter {chapter_number}")
    success = await sqs_service.send_biography_job(job_id=job_id, use_iterative=True)
    
    if not success:
        # Revert job status if failed to queue
        job.status = JobStatus.PENDING
        db.commit()
        raise HTTPException(status_code=500, detail="Failed to queue sequential job for processing")
    
    return {
        "message": f"Sequential biography processing queued for chapter {chapter_number or 'next'}", 
        "job_id": job_id, 
        "status": "iterative_writing",
        "chapter_number": chapter_number
    }

# Background task functions removed - now using SQS for job processing

@router.get("/jobs/summary")
async def get_jobs_summary(db: Session = Depends(get_db)):
    """Get summary of all biography generation jobs (without large content fields)."""
    jobs = db.query(BiographyJob).order_by(BiographyJob.created_at.desc()).all()
    
    # Return only essential fields for performance
    summary_jobs = []
    for job in jobs:
        summary_jobs.append({
            "id": job.id,
            "user_name": job.user_name,
            "email": job.email,
            "status": job.status,
            "progress_percentage": job.progress_percentage,
            "error_message": job.error_message,
            "created_at": job.created_at,
            "updated_at": job.updated_at,
            "completed_at": job.completed_at,
            "processing_time_seconds": job.processing_time_seconds,
            "total_chapters": job.total_chapters,
            "completed_chapters": job.completed_chapters,
            "has_output": bool(job.output_pdf_path and os.path.exists(job.output_pdf_path)) if job.output_pdf_path else False
        })
    
    return summary_jobs

@router.get("/jobs", response_model=List[BiographyJobResponse])
async def get_all_jobs(db: Session = Depends(get_db)):
    """Get all biography generation jobs."""
    jobs = db.query(BiographyJob).order_by(BiographyJob.created_at.desc()).all()
    return jobs

@router.get("/jobs/{job_id}", response_model=BiographyJobResponse)
async def get_job(job_id: str, db: Session = Depends(get_db)):
    """Get a specific biography generation job."""
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job

@router.get("/jobs/{job_id}/download")
async def download_biography(job_id: str, db: Session = Depends(get_db)):
    """Download the generated biography PDF."""
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job.status != JobStatus.COMPLETED or not job.output_pdf_path:
        raise HTTPException(status_code=400, detail="Biography not yet completed")
    
    if not os.path.exists(job.output_pdf_path):
        raise HTTPException(status_code=404, detail="PDF file not found")
    
    return FileResponse(
        job.output_pdf_path,
        media_type='application/pdf',
        filename=f"biography_{job.user_name}_{job.id}.pdf"
    )

@router.delete("/jobs/{job_id}")
async def delete_job(job_id: str, db: Session = Depends(get_db)):
    """Delete a biography generation job and its files."""
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Remove associated files
    if job.uploaded_pdf_path and os.path.exists(job.uploaded_pdf_path):
        os.remove(job.uploaded_pdf_path)
    
    if job.output_pdf_path and os.path.exists(job.output_pdf_path):
        os.remove(job.output_pdf_path)
    
    # Delete from database
    db.delete(job)
    db.commit()
    
    return {"message": "Job deleted successfully"}

@router.post("/jobs/{job_id}/reset")
async def reset_job(job_id: str, db: Session = Depends(get_db)):
    """Reset a failed job back to pending status."""
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Reset job status and clear error fields
    job.status = JobStatus.PENDING
    job.progress_percentage = 0.0
    job.error_message = None
    job.outline_content = None
    job.biography_content = None
    job.evaluation_content = None
    job.final_biography_content = None
    job.output_pdf_path = None
    job.completed_at = None
    job.processing_time_seconds = None
    
    db.commit()
    db.refresh(job)
    
    return {"message": "Job reset successfully", "job_id": job_id}

@router.get("/jobs/{job_id}/status")
async def get_job_status(job_id: str, db: Session = Depends(get_db)):
    """Get enhanced status of a biography generation job with processing strategy details."""
    biography_service = BiographyService(db)
    status = await biography_service.get_job_status(job_id)
    
    if "error" in status:
        raise HTTPException(status_code=404, detail=status["error"])
    
    return status

@router.get("/jobs/{job_id}/statistics")
async def get_job_statistics(job_id: str, db: Session = Depends(get_db)):
    """Get detailed statistics about biography generation including chapter details."""
    biography_service = BiographyService(db)
    stats = biography_service.get_biography_statistics(job_id)
    
    if "error" in stats:
        raise HTTPException(status_code=404, detail=stats["error"])
    
    return stats

@router.post("/jobs/{job_id}/resume")
async def resume_job_processing(job_id: str, db: Session = Depends(get_db)):
    """Resume biography processing from where it left off.
    
    Useful for failed jobs or continuing progressive generation.
    """
    job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    if job.status == JobStatus.COMPLETED:
        raise HTTPException(status_code=400, detail="Job is already completed")

    # Send resume job to SQS queue
    logger.info(f"Sending resume request for biography job {job_id} to SQS queue")
    success = await sqs_service.send_biography_job(job_id=job_id, use_iterative=True)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to queue job for resuming")
    
    return {
        "message": "Biography processing resume queued", 
        "job_id": job_id, 
        "status": "resume_queued",
        "previous_progress": job.progress_percentage
    }

@router.websocket("/jobs/{job_id}/ws")
async def websocket_job_status(websocket: WebSocket, job_id: str):
    """WebSocket endpoint for real-time job status updates."""
    
    short_job_id = job_id[:8]
    logger.info(f"🔌 WebSocket connection attempt for job {short_job_id}")
    logger.info(f"🌐 Client info: {websocket.client}")
    
    # Verify job exists first
    db = SessionLocal()
    try:
        logger.info(f"🗄️ Checking if job {short_job_id} exists in database...")
        job = db.query(BiographyJob).filter(BiographyJob.id == job_id).first()
        if not job:
            logger.warning(f"❌ Job {short_job_id} not found, closing WebSocket")
            await websocket.close(code=4004, reason="Job not found")
            return
        logger.info(f"✅ Job {short_job_id} found, current status: {job.status}, progress: {job.progress_percentage}%")
    except Exception as e:
        logger.error(f"💥 Database error while checking job {short_job_id}: {e}")
        await websocket.close(code=4005, reason="Database error")
        return
    finally:
        db.close()
        logger.info(f"🔒 Database session closed for job verification {short_job_id}")
    
    # Accept WebSocket connection
    logger.info(f"🤝 Accepting WebSocket connection for job {short_job_id}")
    try:
        await manager.connect(websocket, job_id)
        logger.info(f"✅ WebSocket connection established and registered for job {short_job_id}")
    except Exception as e:
        logger.error(f"💥 Failed to establish WebSocket connection for job {short_job_id}: {e}")
        return
    
    try:
        # Send initial job status
        logger.info(f"📤 Sending initial status for job {short_job_id}")
        await manager.broadcast_job_update(job_id)
        logger.info(f"✅ Initial status sent for job {short_job_id}")
        
        # Keep connection alive and listen for client messages (if needed)
        logger.info(f"👂 Listening for messages from client for job {short_job_id}")
        message_count = 0
        
        while True:
            try:
                # Wait for any message from client (ping/pong, etc.)
                logger.debug(f"⏳ Waiting for message from client for job {short_job_id}")
                data = await websocket.receive_text()
                message_count += 1
                logger.info(f"📩 Received message #{message_count} from client for job {short_job_id}: {data[:100]}{'...' if len(data) > 100 else ''}")
                
                # Handle client messages if needed
                if data == "ping":
                    await websocket.send_text("pong")
                    logger.info(f"🏓 Sent pong to client for job {short_job_id}")
                elif data == "status":
                    # Send current status on request
                    logger.info(f"📊 Client requested status update for job {short_job_id}")
                    await manager.broadcast_job_update(job_id)
                    logger.info(f"✅ Status update sent in response to client request for job {short_job_id}")
                elif data.startswith("echo:"):
                    # Echo back for testing
                    echo_message = data[5:]
                    await websocket.send_text(f"Echo: {echo_message}")
                    logger.info(f"🔊 Echoed message to client for job {short_job_id}: {echo_message}")
                else:
                    logger.info(f"❓ Unknown message type from client for job {short_job_id}: {data}")
                    
            except WebSocketDisconnect as e:
                logger.info(f"👋 WebSocket client disconnected for job {short_job_id}: code={e.code}")
                break
            except Exception as e:
                logger.error(f"💥 Error in WebSocket message handling for job {short_job_id}: {e}")
                logger.error(f"💥 WebSocket state: client={websocket.client_state.name}, app={websocket.application_state.name}")
                break
                
    except Exception as e:
        logger.error(f"💥 Unexpected error in WebSocket endpoint for job {short_job_id}: {e}")
    finally:
        logger.info(f"🧹 Cleaning up WebSocket connection for job {short_job_id}")
        try:
            await manager.disconnect(websocket, job_id)
            logger.info(f"✅ WebSocket cleanup completed for job {short_job_id}")
        except Exception as e:
            logger.error(f"💥 Error during WebSocket cleanup for job {short_job_id}: {e}") 

@router.post("/generate", response_model=BiographyJobResponse)
async def generate_biography(
    biography_data: BiographyJobCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    try:
        service = BiographyService(db)
        
        # Create new job
        job = await service.create_biography_job(biography_data)
        
        # Add intelligent processing task to background
        background_tasks.add_task(service.process_biography_intelligently, job.id)
        
        return BiographyJobResponse(
            id=job.id,
            status=job.status.value,
            message="Biography generation started with intelligent processing strategy",
            progress_percentage=0.0
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start biography generation: {str(e)}") 