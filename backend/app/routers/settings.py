from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.models.base import get_db
from app.models.app_settings import AppSettings
from app.schemas.app_settings import (
    AppSettingCreate, 
    AppSettingResponse, 
    AppSettingUpdate,
    OpenAIModelSettings
)

router = APIRouter(prefix="/api/settings", tags=["settings"])

@router.get("/openai-models", response_model=OpenAIModelSettings)
async def get_openai_model_settings(db: Session = Depends(get_db)):
    """Get current OpenAI model settings."""
    # Get current model setting
    model_setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "openai_model",
        AppSettings.is_active == True
    ).first()
    
    current_model = model_setting.setting_value if model_setting else "gpt-3.5-turbo"
    
    # Get available models based on current AI provider
    from app.core.config import settings as app_settings
    
    if app_settings.ai_provider == "anthropic":
        available_models = [
            "claude-3-haiku-20240307",
            "claude-3-sonnet-20240229", 
            "claude-3-opus-20240229",
            # Also include OpenAI models for reference/mapping
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4o"
        ]
    else:
        available_models = [
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-turbo-preview", 
            "gpt-4o",
            "gpt-4o-mini"
        ]
    
    return OpenAIModelSettings(
        openai_model=current_model,
        available_models=available_models
    )

@router.post("/openai-model")
async def update_openai_model(
    model_name: str,
    db: Session = Depends(get_db)
):
    """Update the OpenAI model setting."""
    
    # Get valid models based on current AI provider
    from app.core.config import settings as app_settings
    
    if app_settings.ai_provider == "anthropic":
        valid_models = [
            "claude-3-haiku-20240307",
            "claude-3-sonnet-20240229", 
            "claude-3-opus-20240229",
            # Also allow OpenAI models for mapping
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4", 
            "gpt-4-turbo-preview",
            "gpt-4o",
            "gpt-4o-mini"
        ]
    else:
        valid_models = [
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4", 
            "gpt-4-turbo-preview",
            "gpt-4o",
            "gpt-4o-mini"
        ]
    
    if model_name not in valid_models:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid model. Must be one of: {', '.join(valid_models)}"
        )
    
    # Get or create setting
    setting = db.query(AppSettings).filter(
        AppSettings.setting_key == "openai_model"
    ).first()
    
    if setting:
        setting.setting_value = model_name
        setting.is_active = True
    else:
        setting = AppSettings(
            setting_key="openai_model",
            setting_value=model_name,
            description="Currently selected OpenAI model for biography generation",
            is_active=True
        )
        db.add(setting)
    
    db.commit()
    db.refresh(setting)
    
    return {
        "message": f"OpenAI model updated to {model_name}",
        "model": model_name
    }

@router.get("/", response_model=List[AppSettingResponse])
async def get_all_settings(db: Session = Depends(get_db)):
    """Get all application settings."""
    settings = db.query(AppSettings).filter(AppSettings.is_active == True).all()
    return settings

@router.post("/", response_model=AppSettingResponse)
async def create_setting(
    setting_data: AppSettingCreate,
    db: Session = Depends(get_db)
):
    """Create a new application setting."""
    
    # Check if setting already exists
    existing = db.query(AppSettings).filter(
        AppSettings.setting_key == setting_data.setting_key
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail=f"Setting with key '{setting_data.setting_key}' already exists"
        )
    
    setting = AppSettings(
        setting_key=setting_data.setting_key,
        setting_value=setting_data.setting_value,
        description=setting_data.description,
        is_active=True
    )
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    return setting

@router.put("/{setting_id}", response_model=AppSettingResponse)
async def update_setting(
    setting_id: str,
    setting_update: AppSettingUpdate,
    db: Session = Depends(get_db)
):
    """Update an existing setting."""
    
    setting = db.query(AppSettings).filter(AppSettings.id == setting_id).first()
    if not setting:
        raise HTTPException(status_code=404, detail="Setting not found")
    
    setting.setting_value = setting_update.setting_value
    if setting_update.description is not None:
        setting.description = setting_update.description
    
    db.commit()
    db.refresh(setting)
    
    return setting 