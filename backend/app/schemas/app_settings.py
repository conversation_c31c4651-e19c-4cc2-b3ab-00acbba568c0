from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class AppSettingCreate(BaseModel):
    setting_key: str
    setting_value: str
    description: Optional[str] = None

class AppSettingUpdate(BaseModel):
    setting_value: str
    description: Optional[str] = None

class AppSettingResponse(BaseModel):
    id: str
    setting_key: str
    setting_value: str
    description: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class OpenAIModelSettings(BaseModel):
    openai_model: str
    available_models: list[str] = [
        "gpt-3.5-turbo",
        "gpt-3.5-turbo-16k", 
        "gpt-4",
        "gpt-4-turbo-preview",
        "gpt-4o",
        "gpt-4o-mini"
    ] 