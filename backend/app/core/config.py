from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    database_url: str = "postgresql://postgres:<EMAIL>/biodb"
    
    # AI Service Configuration - ONLY read from .env file, NOT from environment
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    ai_provider: str = "openai"  # Options: openai, anthropic, local
    openai_model: str = "gpt-3.5-turbo"  # Default OpenAI model
    
    # SQS Configuration (replacing Redis)
    # ... existing code ...
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # File Storage
    upload_directory: str = "./uploads"
    output_directory: str = "./output"
    max_file_size_mb: int = 50
    
    # Cloud Storage (Optional)
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_bucket_name: Optional[str] = None
    aws_region: str = "eu-north-1"
    
    # Application Settings
    app_name: str = "Biography Generator"
    version: str = "1.0.0"
    debug: bool = True
    cors_origins: List[str] = ["http://localhost:3000", "ws://localhost:3000", "http://127.0.0.1:3000", "ws://127.0.0.1:3000"]
    
    # SQS Configuration (replacing Celery + Redis)
    sqs_queue_url: Optional[str] = "https://sqs.eu-north-1.amazonaws.com/175071295085/bio-gen.fifo"
    sqs_dlq_queue_url: Optional[str] = None  # Dead Letter Queue for failed jobs
    max_retries: int = 3
    visibility_timeout_seconds: int = 900  # 15 minutes for job processing
    message_retention_period: int = 1209600  # 14 days
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        # Force reading from .env file ONLY - ignore system environment variables for sensitive keys
        case_sensitive = False

# Clear problematic environment variables before creating settings
print("🔧 Config: Clearing OpenAI/Anthropic environment variables to force .env file usage")
env_vars_to_clear = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY']
for var in env_vars_to_clear:
    if var in os.environ:
        print(f"🧹 Config: Removing {var} from environment")
        del os.environ[var]

# Create settings instance
settings = Settings()

# Validate that we have the required API keys
if settings.ai_provider == "openai" and not settings.openai_api_key:
    print("❌ Config: OpenAI provider selected but no API key found in .env file")
elif settings.ai_provider == "anthropic" and not settings.anthropic_api_key:
    print("❌ Config: Anthropic provider selected but no API key found in .env file")
else:
    if settings.openai_api_key:
        print(f"✅ Config: OpenAI API key loaded from .env (ending with: ...{settings.openai_api_key[-6:]})")
    if settings.anthropic_api_key:
        print(f"✅ Config: Anthropic API key loaded from .env")

print(f"🤖 Config: AI Provider set to: {settings.ai_provider}")

# Ensure directories exist
os.makedirs(settings.upload_directory, exist_ok=True)
os.makedirs(settings.output_directory, exist_ok=True) 