#!/usr/bin/env python3

import sys
import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.models.base import SessionLocal
from app.models.biography_job import BiographyJob
from app.schemas.biography_job import JobStatusEnum

def create_test_job():
    """Create a test job for WebSocket testing."""
    
    db: Session = SessionLocal()
    
    try:
        # Create a new test job
        job_id = str(uuid.uuid4())
        
        new_job = BiographyJob(
            id=job_id,
            user_name="WebSocket Test User",
            email="<EMAIL>",
            uploaded_pdf_path=f"/tmp/test_{job_id}.pdf",  # Fake path
            status=JobStatusEnum.COMPLETED,  # Start as completed for testing
            progress_percentage=100.0,  # Start at 100% 
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        db.add(new_job)
        db.commit()
        db.refresh(new_job)
        
        print(f"✅ Created test job: {job_id}")
        print(f"📊 Status: {new_job.status}")
        print(f"📈 Progress: {new_job.progress_percentage}%")
        print(f"👤 User: {new_job.user_name}")
        print("")
        print(f"🧪 Test WebSocket with:")
        print(f"python test_progress_simulation.py {job_id}")
        print("")
        print(f"🌐 Or open frontend at: http://localhost:3000")
        
        return job_id
        
    except Exception as e:
        print(f"❌ Error creating test job: {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    job_id = create_test_job()
    if job_id:
        sys.exit(0)
    else:
        sys.exit(1) 